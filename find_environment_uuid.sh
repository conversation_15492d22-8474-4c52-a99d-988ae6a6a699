#!/bin/bash

# Parámetros de autenticación Bitbucket
BITBUCKET_EMAIL="<EMAIL>"
BITBUCKET_TOKEN="ATATT3xFfGF02G0l8DybmmmZieUsVYEylGp_4oOVJyj4UFsDCeLVXeV7jGZz3QY71YvlQum66D9aXD0MDf5nO46WCwVJOKXKKpGoUiB266SfWh8rdTBEHzxmFbY5bilD1OUd87mO68r-DLekchDqSkUyUlOmZrRt_Nki8ABq3u_rdoL_8HTftp4=89EB88C2"
REPO_OWNER="git-m4u"
REPO_SLUG="ws-mailer"

echo "🔍 Buscando el UUID correcto del entorno de deployment..."

# Lista de UUIDs comunes que podrían funcionar
POSSIBLE_UUIDS=(
    "2d8b0513-13b4-40ba-9f91-7614c13d1746"
    "{2d8b0513-13b4-40ba-9f91-7614c13d1746}"
    "production"
    "staging"
    "development"
    "test"
)

echo "🧪 Probando diferentes UUIDs de entorno..."

for uuid in "${POSSIBLE_UUIDS[@]}"; do
    echo ""
    echo "🔧 Probando UUID: $uuid"
    
    # URL encode el UUID si contiene llaves
    encoded_uuid=$(printf '%s' "$uuid" | sed 's/{/%7B/g; s/}/%7D/g')
    
    # Intentar crear una variable de prueba
    response=$(curl -s -w "\n%{http_code}" -X POST \
        "https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/deployments_config/environments/${encoded_uuid}/variables/" \
        -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}" \
        -H "Content-Type: application/json" \
        -d '{
            "key": "TEST_DEPLOYMENT_VAR",
            "value": "test_value",
            "secured": false
        }')
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    echo "Status: $status_code"
    
    case "$status_code" in
        201)
            echo "✅ ¡UUID CORRECTO ENCONTRADO!: $uuid"
            echo "Respuesta: $body"
            
            # Eliminar la variable de prueba
            var_uuid=$(echo "$body" | jq -r '.uuid' 2>/dev/null)
            if [ "$var_uuid" != "null" ] && [ -n "$var_uuid" ]; then
                echo "🗑️ Eliminando variable de prueba..."
                delete_response=$(curl -s -w "\n%{http_code}" -X DELETE \
                    "https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/deployments_config/environments/${encoded_uuid}/variables/${var_uuid}" \
                    -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}")
                delete_status=$(echo "$delete_response" | tail -n1)
                echo "Delete status: $delete_status"
            fi
            
            echo ""
            echo "🎉 UUID del entorno encontrado: $uuid"
            echo "URL para crear variables: https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/deployments_config/environments/${encoded_uuid}/variables/"
            exit 0
            ;;
        404)
            echo "❌ UUID no válido: $uuid"
            ;;
        403)
            echo "⚠️ Sin permisos para este UUID: $uuid"
            ;;
        *)
            echo "❓ Respuesta inesperada: $body"
            ;;
    esac
done

echo ""
echo "❌ No se pudo encontrar un UUID válido para el entorno de deployment."
echo ""
echo "💡 Sugerencias:"
echo "1. Verifica en la interfaz web de Bitbucket el UUID del entorno"
echo "2. Ve a: https://bitbucket.org/git-m4u/ws-mailer/admin/addon/admin/pipelines/deployment-settings"
echo "3. Inspecciona el elemento o revisa la URL para encontrar el UUID correcto"
echo "4. También puedes crear un nuevo entorno si no existe"
