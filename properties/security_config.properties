# Configuración de seguridad para variables de deployment
# Formato: VARIABLE_NAME=true/false
# true = variable segura (secured)
# false = variable no segura (visible)

# Credenciales de MailJet - SEGURAS
MJ_CLIENT_ID=true
MJ_CLIENT_SECRET=true

# Direcciones de email - NO SEGURAS (pueden ser visibles)
APP1_FROM_ADDRESS=false
APP1_FROM_NAME=false
APP2_FROM_ADDRESS=false
APP2_FROM_NAME=false
APP3_FROM_ADDRESS=false
APP3_FROM_NAME=false

# Credenciales de base de datos - SEGURAS
DB_MYSQL_URL=true
DB_MYSQL_USER=true
DB_MYSQL_PASSWORD=true
