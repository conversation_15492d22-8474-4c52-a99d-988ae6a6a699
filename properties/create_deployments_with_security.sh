#!/bin/bash

# Parámetros de autenticación Bitbucket
BITBUCKET_EMAIL="<EMAIL>"
BITBUCKET_TOKEN="ATATT3xFfGF02G0l8DybmmmZieUsVYEylGp_4oOVJyj4UFsDCeLVXeV7jGZz3QY71YvlQum66D9aXD0MDf5nO46WCwVJOKXKKpGoUiB266SfWh8rdTBEHzxmFbY5bilD1OUd87mO68r-DLekchDqSkUyUlOmZrRt_Nki8ABq3u_rdoL_8HTftp4=89EB88C2"
REPO_OWNER="git-m4u"
REPO_SLUG="ws-mailer"

# API URL para variables de deployment environment
ENV_UUID="2d8b0513-13b4-40ba-9f91-7614c13d1746"
ENV_UUID_ENCODED="%7B${ENV_UUID}%7D"
API_URL="https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/deployments_config/environments/${ENV_UUID_ENCODED}/variables/"

# Archivos de configuración
PROPERTIES_FILE="deployment.properties"
SECURITY_CONFIG_FILE="security_config.properties"

# Función para obtener configuración de seguridad
get_security_setting() {
    local var_name="$1"
    local default_secured="true"  # Por defecto, las variables son seguras
    
    if [[ -f "$SECURITY_CONFIG_FILE" ]]; then
        local setting=$(grep "^${var_name}=" "$SECURITY_CONFIG_FILE" 2>/dev/null | cut -d'=' -f2)
        if [[ -n "$setting" ]]; then
            echo "$setting"
            return
        fi
    fi
    
    echo "$default_secured"
}

# Validar archivos
if [[ ! -f "$PROPERTIES_FILE" ]]; then
    echo "❌ Archivo $PROPERTIES_FILE no encontrado."
    exit 1
fi

if [[ ! -f "$SECURITY_CONFIG_FILE" ]]; then
    echo "⚠️ Archivo $SECURITY_CONFIG_FILE no encontrado. Usando configuración por defecto (todas las variables seguras)."
fi

echo "📦 Leyendo variables desde $PROPERTIES_FILE..."
echo "🔒 Usando configuración de seguridad desde $SECURITY_CONFIG_FILE..."

# Leer y procesar el archivo línea por línea
while IFS='=' read -r key value; do
    # Ignorar líneas vacías o comentarios
    [[ -z "$key" || "$key" =~ ^# ]] && continue

    # Eliminar espacios en blanco
    key=$(echo "$key" | xargs)
    value=$(echo "$value" | xargs)

    # Obtener configuración de seguridad para esta variable
    secured=$(get_security_setting "$key")
    
    # Escapar comillas en el valor
    value_escaped=$(printf '%s' "$value" | sed 's/"/\\"/g')

    # Mostrar información de la variable
    echo "🔧 Creando variable: $key..."
    echo "URL: $API_URL"
    echo "Key: $key"
    echo "Value (escaped): $value_escaped"
    echo "Secured: $secured"

    # Construir los argumentos de curl
    CURL_ARGS=(
        -s
        -w "\n%{http_code}"
        -X POST
        "$API_URL"
        -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}"
        -H "Content-Type: application/json"
        -d "{
              \"key\": \"${key}\",
              \"value\": \"${value_escaped}\",
              \"secured\": ${secured}
            }"
    )

    # Ejecutar curl y capturar la respuesta
    response=$(curl "${CURL_ARGS[@]}")

    # Extraer código de estado y cuerpo de la respuesta
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')

    # Validar el código de respuesta
    case "$status_code" in
        201)
            if [[ "$secured" == "true" ]]; then
                echo "✅ Variable '$key' creada correctamente (SEGURA)."
            else
                echo "✅ Variable '$key' creada correctamente (VISIBLE)."
            fi
            ;;
        409)
            echo "⚠️  Variable '$key' ya existe."
            ;;
        400)
            echo "❌ Error de solicitud ($status_code) al crear '$key'."
            echo "Respuesta de la API: $body"
            ;;
        403)
            echo "❌ Sin permisos ($status_code) para crear '$key'."
            ;;
        404)
            echo "❌ Entorno no encontrado ($status_code) al crear '$key'."
            ;;
        *)
            echo "❌ Error inesperado ($status_code) al crear '$key'."
            echo "Respuesta de la API: $body"
            ;; 
    esac

    echo "------------------------------------------"

done < "$PROPERTIES_FILE"

echo ""
echo "🎉 Proceso completado!"
echo "📋 Resumen:"
echo "   - Variables seguras (🔒): se muestran como *** en la interfaz"
echo "   - Variables visibles (👁️): se pueden ver en la interfaz"
