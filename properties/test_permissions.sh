#!/bin/bash

# Parámetros de autenticación Bitbucket
BITBUCKET_EMAIL="<EMAIL>"
BITBUCKET_TOKEN="ATATT3xFfGF02G0l8DybmmmZieUsVYEylGp_4oOVJyj4UFsDCeLVXeV7jGZz3QY71YvlQum66D9aXD0MDf5nO46WCwVJOKXKKpGoUiB266SfWh8rdTBEHzxmFbY5bilD1OUd87mO68r-DLekchDqSkUyUlOmZrRt_Nki8ABq3u_rdoL_8HTftp4=89EB88C2"
REPO_OWNER="git-m4u"
REPO_SLUG="ws-mailer"

echo "🔍 Verificando permisos del token..."

# Verificar permisos del token
echo "📋 Intentando listar variables existentes..."
response=$(curl -s -w "\n%{http_code}" -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}" \
  "https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/pipelines_config/variables/")

status_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

echo "Status Code: $status_code"
echo "Response Body:"
echo "$body" | jq . 2>/dev/null || echo "$body"

if [ "$status_code" = "200" ]; then
    echo "✅ Permisos correctos para variables de pipeline"
elif [ "$status_code" = "403" ]; then
    echo "❌ Sin permisos para variables de pipeline"
    echo "🔄 Intentando con variables de workspace..."
    
    # Intentar con variables de workspace
    workspace_response=$(curl -s -w "\n%{http_code}" -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}" \
      "https://api.bitbucket.org/2.0/workspaces/git-m4u/pipelines-config/variables/")
    
    workspace_status=$(echo "$workspace_response" | tail -n1)
    workspace_body=$(echo "$workspace_response" | sed '$d')
    
    echo "Workspace Status Code: $workspace_status"
    echo "Workspace Response:"
    echo "$workspace_body" | jq . 2>/dev/null || echo "$workspace_body"
else
    echo "❌ Error inesperado: $status_code"
fi

echo ""
echo "🔧 Intentando crear una variable de prueba..."

# Intentar crear una variable de prueba
test_response=$(curl -s -w "\n%{http_code}" -X POST \
  "https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/pipelines_config/variables/" \
  -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "TEST_VAR",
    "value": "test_value",
    "secured": false
  }')

test_status=$(echo "$test_response" | tail -n1)
test_body=$(echo "$test_response" | sed '$d')

echo "Test Variable Creation Status: $test_status"
echo "Test Response:"
echo "$test_body" | jq . 2>/dev/null || echo "$test_body"
