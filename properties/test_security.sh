#!/bin/bash

# Script para probar la configuración de seguridad con variables de prueba

# Parámetros de autenticación Bitbucket
BITBUCKET_EMAIL="<EMAIL>"
BITBUCKET_TOKEN="ATATT3xFfGF02G0l8DybmmmZieUsVYEylGp_4oOVJyj4UFsDCeLVXeV7jGZz3QY71YvlQum66D9aXD0MDf5nO46WCwVJOKXKKpGoUiB266SfWh8rdTBEHzxmFbY5bilD1OUd87mO68r-DLekchDqSkUyUlOmZrRt_Nki8ABq3u_rdoL_8HTftp4=89EB88C2"
REPO_OWNER="git-m4u"
REPO_SLUG="ws-mailer"

# API URL para variables de deployment environment
ENV_UUID="2d8b0513-13b4-40ba-9f91-7614c13d1746"
ENV_UUID_ENCODED="%7B${ENV_UUID}%7D"
API_URL="https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/deployments_config/environments/${ENV_UUID_ENCODED}/variables/"

echo "🧪 Probando configuración de seguridad..."

# Crear variable segura de prueba
echo "🔒 Creando variable SEGURA de prueba..."
response_secure=$(curl -s -w "\n%{http_code}" -X POST "$API_URL" \
    -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "key": "TEST_SECURE_VAR",
        "value": "secret_value_123",
        "secured": true
    }')

status_secure=$(echo "$response_secure" | tail -n1)
body_secure=$(echo "$response_secure" | sed '$d')

if [ "$status_secure" = "201" ]; then
    echo "✅ Variable segura creada correctamente"
    secure_uuid=$(echo "$body_secure" | jq -r '.uuid' 2>/dev/null)
else
    echo "❌ Error al crear variable segura: $status_secure"
    echo "$body_secure"
fi

echo ""

# Crear variable visible de prueba
echo "👁️ Creando variable VISIBLE de prueba..."
response_visible=$(curl -s -w "\n%{http_code}" -X POST "$API_URL" \
    -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "key": "TEST_VISIBLE_VAR",
        "value": "visible_value_456",
        "secured": false
    }')

status_visible=$(echo "$response_visible" | tail -n1)
body_visible=$(echo "$response_visible" | sed '$d')

if [ "$status_visible" = "201" ]; then
    echo "✅ Variable visible creada correctamente"
    visible_uuid=$(echo "$body_visible" | jq -r '.uuid' 2>/dev/null)
else
    echo "❌ Error al crear variable visible: $status_visible"
    echo "$body_visible"
fi

echo ""
echo "🎯 Resultados del test:"
echo "   - Variable segura: TEST_SECURE_VAR (debería aparecer como *** en la interfaz)"
echo "   - Variable visible: TEST_VISIBLE_VAR (debería mostrar el valor completo)"
echo ""
echo "🌐 Verifica en: https://bitbucket.org/git-m4u/ws-mailer/admin/addon/admin/pipelines/deployment-settings"

# Preguntar si quiere eliminar las variables de prueba
echo ""
read -p "¿Quieres eliminar las variables de prueba? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️ Eliminando variables de prueba..."
    
    if [ -n "$secure_uuid" ] && [ "$secure_uuid" != "null" ]; then
        delete_response=$(curl -s -w "\n%{http_code}" -X DELETE \
            "${API_URL}${secure_uuid}" \
            -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}")
        delete_status=$(echo "$delete_response" | tail -n1)
        if [ "$delete_status" = "204" ]; then
            echo "✅ Variable segura eliminada"
        else
            echo "❌ Error eliminando variable segura: $delete_status"
        fi
    fi
    
    if [ -n "$visible_uuid" ] && [ "$visible_uuid" != "null" ]; then
        delete_response=$(curl -s -w "\n%{http_code}" -X DELETE \
            "${API_URL}${visible_uuid}" \
            -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}")
        delete_status=$(echo "$delete_response" | tail -n1)
        if [ "$delete_status" = "204" ]; then
            echo "✅ Variable visible eliminada"
        else
            echo "❌ Error eliminando variable visible: $delete_status"
        fi
    fi
else
    echo "ℹ️ Variables de prueba mantenidas. Puedes eliminarlas manualmente si lo deseas."
fi
