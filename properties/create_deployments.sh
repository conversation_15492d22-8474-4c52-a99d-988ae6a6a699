#!/bin/bash

# Parámetros de autenticación Bitbucket
BITBUCKET_EMAIL="<EMAIL>"         # Reemplaza por tu email de Bitbucket
BITBUCKET_TOKEN="ATATT3xFfGF02G0l8DybmmmZieUsVYEylGp_4oOVJyj4UFsDCeLVXeV7jGZz3QY71YvlQum66D9aXD0MDf5nO46WCwVJOKXKKpGoUiB266SfWh8rdTBEHzxmFbY5bilD1OUd87mO68r-DLekchDqSkUyUlOmZrRt_Nki8ABq3u_rdoL_8HTftp4=89EB88C2"                 # Reemplaza por tu token API
REPO_OWNER="git-m4u"
REPO_SLUG="ws-mailer"

# API URL para variables de deployment environment
ENV_UUID="2d8b0513-13b4-40ba-9f91-7614c13d1746"
# URL encode las llaves del UUID
ENV_UUID_ENCODED="%7B${ENV_UUID}%7D"
API_URL="https://api.bitbucket.org/2.0/repositories/${REPO_OWNER}/${REPO_SLUG}/deployments_config/environments/${ENV_UUID_ENCODED}/variables/"

# Ruta al archivo .properties
PROPERTIES_FILE="deployment.properties"

# Validar si existe el archivo
if [[ ! -f "$PROPERTIES_FILE" ]]; then
  echo "❌ Archivo $PROPERTIES_FILE no encontrado."
  exit 1
fi

echo "📦 Leyendo variables desde $PROPERTIES_FILE..."

# Leer y procesar el archivo línea por línea
while IFS='=' read -r key value; do
  # Ignorar líneas vacías o comentarios
  [[ -z "$key" || "$key" =~ ^# ]] && continue

  # Eliminar espacios en blanco
  key=$(echo "$key" | xargs)
  value=$(echo "$value" | xargs)

  # Escapar comillas en el valor
  value_escaped=$(printf '%s' "$value" | sed 's/"/\\"/g')

  echo "🔧 Creando variable: $key..."
  echo "URL: $API_URL"
  echo "Key: $key"
  echo "Value (escaped): $value_escaped"

  # Construir los argumentos de curl en un array para mayor seguridad
  CURL_ARGS=(
      -s
      -w "\n%{http_code}"
      -X POST
      "$API_URL"
      -u "${BITBUCKET_EMAIL}:${BITBUCKET_TOKEN}"
      -H "Content-Type: application/json"
      -d "{
            \"key\": \"${key}\",
            \"value\": \"${value_escaped}\",
            \"secured\": true
          }"
  )

  # Ejecutar curl y capturar la respuesta
  response=$(curl "${CURL_ARGS[@]}")

  # Extraer código de estado y cuerpo de la respuesta
  status_code=$(echo "$response" | tail -n1)
  body=$(echo "$response" | sed '$d')

  # Validar el código de respuesta
  case "$status_code" in
    201)
      echo "✅ Variable '$key' creada correctamente."
      ;;
    409)
      echo "⚠️  Variable '$key' ya existe."
      ;;
    400)
      echo "❌ Error de solicitud ($status_code) al crear '$key'."
      echo "Respuesta de la API: $body"
      echo "Verifica que el nombre de la variable sea válido."
      ;;
    403)
      echo "❌ Sin permisos ($status_code) para crear '$key'."
      echo "Verifica que el token tenga permisos de pipeline."
      ;;
    404)
      echo "❌ Repositorio no encontrado ($status_code) al crear '$key'."
      echo "Verifica el nombre del repositorio y workspace."
      ;;
    *)
      echo "❌ Error inesperado ($status_code) al crear '$key'."
      echo "Respuesta de la API: $body"
      ;;
  esac

  echo "------------------------------------------"

done < "$PROPERTIES_FILE"